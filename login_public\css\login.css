body,
html,
.ie6_out,
.ie6_in {
  width: 100% !important;
  height: 100% !important;
  position: relative;
  z-index: -1;
}
body *,
html *,
.ie6_out *,
.ie6_in * {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #000;
  -webkit-font-smoothing: antialiased;
}
body {
  overflow: hidden;
}
.font-family {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #000;
  -webkit-font-smoothing: antialiased;
}
.clearfix::after {
  display: block;
  width: 0;
  height: 0;
  content: "";
  clear: both;
}
.body_index {
  background-color: #fff !important;
  background-image: none !important;
}
.oneregional001 {
  width: auto !important;
  position: relative !important;
  min-width: auto !important;
  height: 100%;
  background-color: transparent !important;
  background-image: none !important;
}
.oneregional001,
.ie6_out,
.body_index,
.ie6_in {
  position: relative;
  z-index: -1;
}
.login-bg {
  position: absolute;
  z-index: -1;
  width: 100%;
}
.login-bg {
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 100%;
}
.login-bg >div:first-child{
  height: 100%;
}
.login-bg img {
  width: 100%;
  height: 100%;
}
.titlebar {
  width: auto !important;
  height: 31px !important;
  background-color: #fff !important;
  padding: 25px 60px;
}
.titlebar .logo-left {
  display: inline-block;
  width: 249px;
  height: 31px;
  float: left;
}
.titlebar .logo-left img {
  width: 100%;
}
.titlebar .logo-title {
  float: left;
  border-left: 1px #dbdbdb solid;
  color: #474749;
  font-size: 20px;
  margin-left: 16px;
  padding-left: 16px;
}
.titlebar .logo-right {
  float: right;
}
.titlebar .logo-right a {
  font-size: 16px;
  text-decoration: none;
  display: inline-block;
  margin-left: 50px;
  cursor: pointer;
  color: #474749;
}
.titlebar .logo-right a:hover {
  color: #474749;
}
.titlebar .logo-right img {
  position: relative;
  top: 5px;
  left: -2px;
}
.titlebar .logo-right .login-safe img {
  width: 20px;
  height: 23px;
}
.titlebar .logo-right .login-secret img {
  width: 17px;
  height: 23px;
}
.titlebar .logo-right .login-help img {
  width: 22px;
  height: 22px;
}
.titlebar .logo-right .login-wenhao img {
  width: 20px;
  height: 22px;
}
.login-content-bg {
  position: absolute;
  width: 420px;
  top: 50px;
  right: 100px;
  background-color: #DA0816;
  border-radius: 8px;
  padding-top: 8px;
  z-index: 10;
}
.login-content-bg .login-content {
  background: #fff !important;
  border-radius: 8px;
  padding-bottom: 40px;
  position: relative;
  z-index: 20;
  height: auto;
  margin-left: 0px;
  margin-top: 0px;
  width: auto;
}
.login-content-bg .login-content .login-box-title {
  color: #2b2a2b;
  font-size: 30px;
  text-align: center;
  padding-top: 46px;
  font-weight: 600;
}
.login-content-bg .login-content .login_boxs {
  padding: 0 50px !important;
}
.login-content-bg .login-content .login_left {
  display: block !important;
  text-align: left !important;
  color: #3A3838;
  font-size: 16px !important;
  font-weight: 500;
  width: 100% !important;
}
.login-content-bg .login-content .login-style {
  position: relative;
  height: 90px !important;
}
.login-content-bg .login-content .login_cardNo2 {
  margin-top: 50px !important;
  position: relative;
  height: 90px !important;
}
.login-content-bg .login-content .login_cardNo2 .login_left {
  height: 45px !important;
  line-height: 45px !important;
}
.login-content-bg .login-content .login_cardNo2 div.lui_input_displayer {
  padding-left: 15px;
}
.login-content-bg .login-content .login_cardNo2 #certDNTd,
.login-content-bg .login-content .login_cardNo2 .lui_input_wrapper,
.login-content-bg .login-content .login_cardNo2 #certDNCombox,
.login-content-bg .login-content .login_cardNo2 .lui_input_displayer,
.login-content-bg .login-content .login_cardNo2 .inputTip {
  width: auto !important;
  display: block;
  height: 25px !important;
  line-height: 25px !important;
  display: block !important;
  border: 0px;
}
.login-content-bg .login-content .login_cardNo2 #certDNComboxTr,
.login-content-bg .login-content .login_cardNo2 #certDNTd{
  padding: 10px 0 !important;
  border: 1px solid #ccc;
}
.login-content-bg .login-content .login_cardNo2 input#certDNCombox {
  width: 280px !important;
  padding: 0 20px;
}
.login-content-bg .login-content .login_cardNo2 div.lui_input_displayer span {
  display: block;
  padding: 0 20px;
}
.login-content-bg .login-content .login_cardNo2 div.lui_combobox_button {
  background: none !important;
}
.login-content-bg .login-content .login_cardNo2 div.lui_combobox_button::after {
  content: '';
  transform: rotate(135deg);
  display: block;
  width: 10px;
  height: 10px;
  border-top: 1px solid #928d8d;
  border-right: 1px solid #928d8d;
  position: relative;
  top: 5px;
  right: 4px;
}
.login-content-bg .login-content .login_pswdiv3 {
  position: relative;
  height: 90px !important;
}
.login-content-bg .login-content .login_pswdiv2 {
  position: relative;
  height: 90px !important;
}
.login-content-bg .login-content .login_pswdiv2 input {
  padding: 0 20px;
  border: 1px solid #ccc;
}
.login-content-bg .login-content .login_pswdiv2 .login_left {
  height: 45px !important;
  line-height: 45px !important;
}
.login-content-bg .login-content .login_btn,
.login-content-bg .login-content .login_guide {
  position: relative;
}
.login-content-bg .login-content .login_btn {
  margin: 30px 0px 0px 0px;
}
.login-content-bg .login-content .login_btn #login_button,
.login-content-bg .login-content .login_btn .login_button {
  display: block !important;
  width: 320px !important;
  border-radius: 30px !important;
  height: 45px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #FFFFFF !important;
  background-color: #DA0816 !important;
  border-color: #DA0816 !important;
  text-align: center;
  line-height: 45px !important; 
}
.login-content-bg .login-content .login_btn #login_button span,
.login-content-bg .login-content .login_btn .login_button span {
  color: #FFFFFF !important;
}
.login-content-bg .login-content .login_btn .btn_stepDown_out_l,
.login-content-bg .login-content .login_btn .btn_stepDown_out_r,
.login-content-bg .login-content .login_btn .btn_stepDown_over_r,
.login-content-bg .login-content .login_btn .btn_stepDown_over_l,
.login-content-bg .login-content .login_btn .btn_stepDown_down_l,
.login-content-bg .login-content .login_btn .btn_stepDown_down_r,
.login-content-bg .login-content .login_btn .btn_stepDown_up_l,
.login-content-bg .login-content .login_btn .btn_stepDown_up_r {
  display: none;
}
.login-content-bg .login-content .login_btn .btn_stepDown_out,
.login-content-bg .login-content .login_btn .forgedBtn_stepUp,
.login-content-bg .login-content .login_btn .btn_stepDown_over,
.login-content-bg .login-content .login_btn .btn_stepDown_down {
  background: transparent !important;
  height: 100% !important;
  line-height: 45px;
  font-size: 16px !important;
  width: 100%;
}
.login-content-bg .login-content .login_guide {
  margin: 0px 0px 0px 50px;
}
.login-content-bg .login-content .login_guide p {
  color: #3A3838;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
.login-content-bg .login-content .login_guide a {
  color: blue;
}
.login-footer {
  position: absolute;
  width: 100%;
  bottom: 20px;
  left: 0;
  z-index: 10;
}
.login-footer .login-copyright {
   text-align: center;
   color: #fff;
   font-size: 16px;
   margin-top: 20px;
   margin-right: 15px;
   padding-right: 50px;
  }
.login-footer .login-record {
     text-align: center;
     color: #fff;
     margin-top: 20px !important;
     margin: 0 15px;
}
.login-footer .login-record a {
     font-size: 16px; 
     color: inherit;
     text-decoration: none;
}
.login-footer .login-record a:hover {
     color: inherit;
     text-decoration: none;
}
  .login-footer .login-copyright-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin-top: 20px;
     img {
        margin-top: 20px;
        max-width: 100px;
        max-height: 40px;
        width: auto;
        height: auto; 
       }
    }
  
.login-footer .login-map-wrap {
  width: 560px;
  padding: 8px 0 8px 50px;
  margin: 0 auto;
  border: 1px #D9EDF8 solid;
  border-radius: 30px;
  height: 24px;
}
.login-footer .login-map-wrap .login-map p {
  float: left;
  list-style: none;
  color: #fff;
  font-size: 16px;
  padding-left: 0px !important;
  padding-right: 25px;
  border-right: 1px #D9EDF8 solid;
  margin-right: 25px;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: contain;
  cursor: pointer;
}
.login-footer .login-map-wrap .login-map p a {
  color: #fff;
  line-height: 24px;
}
.login-footer .login-map-wrap .login-map p span {
  vertical-align: super;
  margin-left: 3px;
}
.login-footer .login-map-wrap .login-map .login-demo img{
  width: 23px;
}
.login-footer .login-map-wrap .login-map .login-frozen {
  padding-left: 35px;
}
.login-footer .login-map-wrap .login-map .login-frozen img {
  height: 24px;
}
.login-footer .login-map-wrap .login-map .login-downloadcenter img {
  height: 24px;
}
.login-footer .login-map-wrap .login-map .login-ebook {
  border-right: 0px;
  padding-right: 10px;
  white-space: nowrap;
}
.login-footer .login-map-wrap .login-map .login-ebook img {
  height: 24px;
}
#pinArea input {
  border: 1px #ccc solid;
  width: 283px;
  height: 45px;
  padding-left: 15px;
}
#pinArea input > span,
#pinArea input embed {
  display: inline-block;
  border: 1px #ccc solid;
  width: 100%;
  height: 45px;
}
#pinArea.show-ukey {
  visibility: visible;
}
#pinArea object,
#pinArea #_ocx_pin_pge {
  width: 288px;
  /* height: 45px; */
  padding: 10px 15px;
  display: block;
  border: 1px #ccc solid;
}
#pinArea object, #pinArea embed  {
  width: 258px;
  border: 0px;
  height: 25px;
  padding-top: 0;
  padding-bottom: 0;
}
#pinArea embed {
  border: 1px #ccc solid;
  padding: 10px 30px;
}
#pinArea input[type="password"]::-ms-reveal {
  display: none;
}
.ie6_out{
  overflow: auto;
}
.ie6_in{
  min-height: 850px;
}
.btn_stepDown_out{
  display: block;
}

.preCheck {
  margin-top: 20px;
}

#preCheck input[type='checkbox'] {
  width: 15px;
  height: 15px;
  border: solid 1px gray;
}

#preCheck input[type='checkbox']:checked{
  appearance: none;
  box-sizing: border-box;
  width: 15px;
  height: 15px;
  border: solid 1px #DA0816;
  outline: none;
  cursor: pointer;
  background-color: #DA0816 !important;
  text-align: center;
  line-height: 15px;
}


#preCheck input[type='checkbox']:checked::after{ 
  content: "✓";
  color: #fff;
  font-size: 12px;
}
